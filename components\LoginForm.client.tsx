// components/LoginForm.client.tsx
import { useState, useRef } from 'react';
import { useRedirect } from 'blade/hooks';
import { useQueryState } from 'blade/client/hooks';
import { useUnifiedSession } from '../lib/auth-client';
import type { UserRole } from '../lib/types';
import NoiseText from '../components/home/<USER>';
import FamilyStyleOTP from '../components/ui/family-style-otp.client';
import CustomWheelPicker, { type WheelPickerOption } from '../components/ui/custom-wheel-picker.client';
import { Badge } from '../components/ui/badge.client';
import { Link } from 'blade/client/components';
import {
  InputButton,
  InputButtonProvider,
  InputButtonAction,
  InputButtonSubmit,
  InputButtonInput,
} from '../components/ui/buttons/input';
import { Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'motion/react';
import { toast } from '../components/ui/toast.client';

// Import the unified auth client
import { authClient, emailOtp } from '../lib/auth-client';

type AuthStep = 'role-selection' | 'credentials' | 'otp-verification';

interface LoginFormProps {
  defaultRole?: UserRole;
}

const LoginForm = ({ defaultRole }: LoginFormProps) => {
  // Use useQueryState to manage the role in the URL
  const [roleQuery, setRoleQuery] = useQueryState('role');

  // Determine the current role and step based on the query parameter or default prop
  const selectedRole: UserRole = (roleQuery as UserRole) || defaultRole || 'teacher';
  const currentStep: AuthStep = roleQuery || defaultRole ? 'credentials' : 'role-selection';

  // Local state for the form that does not need to be in the URL
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');
  const [showInput, setShowInput] = useState(false);
  const [showOtpDirectly, setShowOtpDirectly] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);
  const [otpStep, setOtpStep] = useState(false);
  const [provisionalRole, setProvisionalRole] = useState<UserRole>(selectedRole);
  const [studentStep, setStudentStep] = useState<'username' | 'password'>('username');

  // Form fields
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [otp, setOtp] = useState('');

  // Refs
  const inputRef = useRef<HTMLDivElement>(null);
  const messageTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const redirect = useRedirect();
  const { session, refreshSession } = useUnifiedSession();

  // Redirect if already authenticated
  if (session?.user) {
    const rolePrefix = session.user.role === 'school_admin' ? 'school' : session.user.role;
    redirect(`/${rolePrefix}/${session.user.slug}`);
    return null;
  }

  const resetForm = () => {
    // Clear any pending message timeouts
    if (messageTimeoutRef.current) {
      clearTimeout(messageTimeoutRef.current);
    }

    setEmail('');
    setUsername('');
    setPassword('');
    setOtp('');
    setError('');
    setMessage('');
    setMessageType('');
    setShowInput(false);
    setShowOtpDirectly(false);
    setLoading(false);
    setStudentStep('username'); // Reset student step
  };

  const handleRoleSelection = (role: UserRole) => {
    setRoleQuery(role);
    resetForm();
  };

  const handleBack = () => {
    // Clear any pending message timeouts
    if (messageTimeoutRef.current) {
      clearTimeout(messageTimeoutRef.current);
    }

    if (otpStep) {
      setOtpStep(false);
    } else if (currentStep === 'credentials') {
      setRoleQuery(null); // Go back to role selection by clearing the query param
    }

    // Reset student step when going back
    setStudentStep('username');
    setError('');
    setMessage('');
    setMessageType('');
  };

  // Student login (two-step process)
  const handleStudentLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (studentStep === 'username') {
      // Step 1: Validate username and proceed to password
      if (!username.trim()) {
        setMessage('Username is required');
        setMessageType('error');

        // Clear validation error message after 3 seconds
        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 3000);
        return;
      }

      // Clear any previous messages
      setMessage('');
      setMessageType('');

      // Move to password step
      setStudentStep('password');
      setShowInput(true); // Keep input visible for password
      return;
    }

    // Step 2: Authenticate with username and password
    if (!password.trim()) {
      setMessage('Password is required');
      setMessageType('error');

      // Clear validation error message after 3 seconds
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
      messageTimeoutRef.current = setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 3000);
      return;
    }

    setLoading(true);
    setMessage('');
    setMessageType('');

    try {
      let result;
      const usernameOrEmail = username.trim();

      // Check if the input looks like an email address
      const isEmail = usernameOrEmail.includes('@');

      if (isEmail) {
        // Use email login
        result = await authClient.signIn.email({
          email: usernameOrEmail,
          password: password.trim(),
        });
      } else {
        // Use username login
        result = await authClient.signIn.username({
          username: usernameOrEmail,
          password: password.trim(),
        });
      }

      if (result.error) {
        throw new Error(result.error.message || 'Login failed');
      }

      if (result.data?.user) {
        console.log('Student login successful, refreshing session to get updated user data...');

        try {
          // Refresh the session to get the latest user data including custom fields like slug
          const refreshedSession = await refreshSession();
          const user = refreshedSession?.user as any;

          console.log('Student login - refreshed user data:', user);
          console.log('Available fields:', Object.keys(user || {}));
          console.log('Slug:', user?.slug, 'Username:', user?.username, 'ID:', user?.id);

          const userSlug = user?.slug || user?.username || user?.id;
          console.log('Using slug for redirect:', userSlug);

          // Small delay to ensure session state propagates
          setTimeout(() => {
            redirect(`/student/${userSlug}`);
          }, 100);
        } catch (sessionError) {
          console.error('Failed to refresh session for student login:', sessionError);
          // Fallback to using the original user data
          const user = result.data.user as any;
          const userSlug = user.slug || user.username || user.id;
          redirect(`/student/${userSlug}`);
        }
      }
    } catch (error) {
      setMessage(error instanceof Error ? error.message : 'Login failed. Please check your credentials.');
      setMessageType('error');

      // Clear error message after 4 seconds and reset to username step
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
      messageTimeoutRef.current = setTimeout(() => {
        setMessage('');
        setMessageType('');
        // Reset to username step on error
        setUsername('');
        setPassword('');
        setShowInput(false);
        setStudentStep('username');
      }, 4000);
    } finally {
      setLoading(false);
    }
  };

  // Handle direct OTP access for teacher
  const handleTeacherOtpDirect = () => {
    setShowOtpDirectly(true);
    setOtpStep(true);
  };

  // Teacher login (email/password or Google OAuth or OTP)
  const handleTeacherLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // For teacher login, show input first if not already shown
    if (!showInput) {
      setShowInput(true);
      return;
    }

    setLoading(true);
    setMessage('');
    setMessageType('');

    try {
      if (email && !password) {
        // Send OTP
        try {
          const result = await emailOtp.sendVerificationOtp({
            email: email.trim(),
            type: 'sign-in'
          });

          if (result.error) {
            // Handle structured error responses from server
            const errorData = result.error as any;

            // Better Auth wraps server errors, so we need to check multiple levels
            let actualError = errorData;

            // If the error has a 'body' property (from fetch response), parse it
            if (errorData.body && typeof errorData.body === 'string') {
              try {
                actualError = JSON.parse(errorData.body);
              } catch (e) {
                actualError = { message: errorData.body };
              }
            }

            // Check if we have a nested error object
            if (actualError.error && typeof actualError.error === 'object') {
              actualError = actualError.error;
            }

            // Now handle the error based on code or message
            if (actualError.code) {
              switch (actualError.code) {
                case 'WAITLIST_REQUIRED':
                  throw new Error('Your email is not on the waitlist. Please sign up first.');
                case 'WAITLIST_NOT_APPROVED':
                  throw new Error('Your account is awaiting approval. Please check back later.');
                case 'EMAIL_SERVICE_ERROR':
                  throw new Error('Email service is temporarily unavailable. Please try again later.');
                case 'WAITLIST_VALIDATION_ERROR':
                  throw new Error('Unable to verify waitlist status. Please try again later.');
                default:
                  throw new Error(actualError.message || 'Failed to send OTP');
              }
            } else {
              // Handle string error messages or generic objects
              const errorMessage = actualError.message || errorData.message || (typeof errorData === 'string' ? errorData : 'Failed to send OTP');
              throw new Error(errorMessage);
            }
          }

          setMessage('OTP sent to your email. Please check your inbox.');
          setMessageType('success');
          setOtpStep(true);
        } catch (otpError) {
          // Handle specific OTP sending errors
          if (otpError instanceof Error) {
            if (otpError.message.includes('500') || otpError.message.includes('Internal Server Error')) {
              throw new Error('Email service is temporarily unavailable. Please try again later.');
            } else if (otpError.message.includes('WAITLIST_REQUIRED')) {
              throw new Error('Your email is not on the waitlist. Please sign up first.');
            } else if (otpError.message.includes('WAITLIST_NOT_APPROVED')) {
              throw new Error('Your account is awaiting approval. Please check back later.');
            } else {
              throw new Error(otpError.message || 'Failed to send OTP. Please try again.');
            }
          } else {
            throw new Error('Failed to send OTP. Please try again.');
          }
        }
      } else if (email && password) {
        // Email/password login
        const result = await authClient.signIn.email({
          email: email.trim(),
          password: password
        });

        if (result.error) {
          throw new Error(result.error.message || 'Invalid email or password');
        }

        if (result.data?.user) {
          const userSlug = (result.data.user as any).slug || result.data.user.id;
          redirect(`/teacher/${userSlug}`);
        }
      } else {
        setMessage('Please enter your email address');
        setMessageType('error');

        // Clear validation error message after 3 seconds
        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 3000);
      }
    } catch (error) {
      // Enhanced error handling for server errors
      if (error instanceof Error) {
        if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
          setMessage('Server error occurred. Please try again later.');
          setMessageType('error');
        } else if (error.message.includes('Network Error') || error.message.includes('fetch')) {
          setMessage('Network error. Please check your connection and try again.');
          setMessageType('error');
        } else {
          setMessage(error.message);
          setMessageType('error');
        }
      } else {
        setMessage('Login failed. Please try again.');
        setMessageType('error');
      }

      // Clear error message after 4 seconds
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
      messageTimeoutRef.current = setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 4000);
    } finally {
      setLoading(false);
    }
  };

  // Handle direct OTP access for school admin
  const handleSchoolOtpDirect = () => {
    setShowOtpDirectly(true);
    setOtpStep(true);
  };

  // School admin login (email OTP only)
  const handleSchoolLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // For school admin login, show input first if not already shown
    if (!showInput) {
      setShowInput(true);
      return;
    }

    setLoading(true);
    setMessage('');
    setMessageType('');

    try {
      if (!email.trim()) {
        setMessage('Email address is required');
        setMessageType('error');

        // Clear validation error message after 3 seconds
        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 3000);
        return;
      }

      // Send OTP with role information
      try {
        // Store the selected role in a way the router can access it
        const result = await fetch('/api/auth/email-otp/send-verification-otp', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Selected-Role': selectedRole // Add role to headers
          },
          body: JSON.stringify({
            email: email.trim(),
            type: 'sign-in'
          })
        });

        const responseData = await result.json();

        if (!result.ok || responseData.error) {
          // Handle error responses
          const errorData = responseData.error || responseData;

          // Handle the error based on code or message
          if (errorData.code) {
            switch (errorData.code) {
              case 'WAITLIST_REQUIRED':
                throw new Error('Your email is not on the waitlist. Please sign up first.');
              case 'WAITLIST_NOT_APPROVED':
                throw new Error('Your account is awaiting approval. Please check back later.');
              case 'WRONG_ROLE_SELECTED':
                throw new Error(errorData.message || 'Please use the correct sign-in option for your role.');
              case 'EMAIL_SERVICE_ERROR':
                throw new Error('Email service is temporarily unavailable. Please try again later.');
              case 'WAITLIST_VALIDATION_ERROR':
                throw new Error('Unable to verify waitlist status. Please try again later.');
              default:
                throw new Error(errorData.message || 'Failed to send OTP');
            }
          } else {
            throw new Error(errorData.message || 'Failed to send OTP');
          }
        }

        setMessage('OTP sent to your email. Please check your inbox.');
        setMessageType('success');
        setOtpStep(true);
      } catch (otpError) {
        // Handle specific OTP sending errors for school admin
        if (otpError instanceof Error) {
          if (otpError.message.includes('500') || otpError.message.includes('Internal Server Error')) {
            throw new Error('Email service is temporarily unavailable. Please try again later.');
          } else if (otpError.message.includes('WAITLIST_REQUIRED')) {
            throw new Error('Your email is not on the waitlist. Please sign up first.');
          } else if (otpError.message.includes('WAITLIST_NOT_APPROVED')) {
            throw new Error('Your account is awaiting approval. Please check back later.');
          } else {
            throw new Error(otpError.message || 'Failed to send OTP. Please try again.');
          }
        } else {
          throw new Error('Failed to send OTP. Please try again.');
        }
      }
    } catch (error) {
      // Enhanced error handling for server errors
      if (error instanceof Error) {
        if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
          setMessage('Server error occurred. Please try again later.');
          setMessageType('error');
        } else if (error.message.includes('Network Error') || error.message.includes('fetch')) {
          setMessage('Network error. Please check your connection and try again.');
          setMessageType('error');
        } else {
          setMessage(error.message);
          setMessageType('error');
        }
      } else {
        setMessage('Failed to send OTP. Please try again.');
        setMessageType('error');
      }

      // Clear error message after 4 seconds
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
      messageTimeoutRef.current = setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 4000);
    } finally {
      setLoading(false);
    }
  };

  // OTP verification
  const handleOtpVerification = async (otpValue?: string) => {
    const otpToVerify = otpValue || otp;
    
    if (!otpToVerify.trim()) {
      setError('Please enter the OTP code');
      return;
    }

    setLoading(true);
    setError('');

    try {
      let result;
      
      result = await authClient.signIn.emailOtp({
        email: email.trim(),
        otp: otpToVerify.trim()
      });

      console.log('OTP Sign-in result:', result);

      if (result?.error) {
        console.log('OTP Sign-in error:', result.error);
        throw new Error(result.error.message || 'Invalid OTP code');
      }

      if (result?.data?.user) {
        const user = result.data.user as any;
        console.log('OTP Sign-in successful, user data:', user);

        // Check if user needs updates (missing role, bad slug, etc.)
        const needsUpdate = !user.role ||
                          !user.slug ||
                          user.slug.startsWith('use_') ||
                          !user.name;

        if (needsUpdate) {
          console.log('User needs updates, updating database...');

          try {
            // Update the user with the selected role and email-based slug
            const email = user.email;
            const userType = selectedRole; // Use the selected role from the form
            const role = userType === 'school_admin' ? 'school_admin' : 'teacher';

            // Generate email-based slug
            const emailSlug = email.replace('@', '-at-')
              .toLowerCase()
              .replace(/[^a-z0-9-]/g, '-')
              .replace(/-+/g, '-')
              .replace(/^-|-$/g, '') || 'user';

            console.log('Updating user in database with role:', role, 'and slug:', emailSlug);

            // Make API call to update user in database
            const updateResponse = await fetch('/api/update-user', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                userId: user.id,
                updates: {
                  role: role,
                  slug: emailSlug,
                  name: user.name || email.split('@')[0],
                  emailVerified: true
                }
              })
            });

            if (updateResponse.ok) {
              console.log('User updated successfully in database');

              // Update the user object for redirect
              user.role = role;
              user.slug = emailSlug;
              user.name = user.name || email.split('@')[0];
            } else {
              console.error('Failed to update user in database');
              // Still update locally for redirect
              user.role = role;
              user.slug = emailSlug;
              user.name = user.name || email.split('@')[0];
            }

            console.log('Updated user data:', user);
          } catch (error) {
            console.error('Error updating user data:', error);
            // Fallback to using selectedRole
            user.role = selectedRole || 'teacher';
          }
        }

        // Determine role for redirect
        const userRole = user.role || selectedRole || 'teacher';
        const rolePrefix = userRole === 'school_admin' ? 'school' : userRole;
        const userSlug = user.slug || user.id;

        console.log('OTP Sign-in successful, refreshing session and redirecting to:', `/${rolePrefix}/${userSlug}`);

        // Use the unified session's refresh method to properly update the React state
        try {
          const refreshedSession = await refreshSession();
          console.log('Session refreshed successfully:', refreshedSession?.user);

          // Wait a moment to ensure the session state has propagated to all hooks
          setTimeout(() => {
            console.log('Executing delayed redirect to:', `/${rolePrefix}/${userSlug}`);
            redirect(`/${rolePrefix}/${userSlug}`);
          }, 300);
        } catch (sessionError) {
          console.error('Failed to refresh session:', sessionError);
          // Fallback to immediate redirect - user will still be authenticated
          console.log('Using fallback redirect to:', `/${rolePrefix}/${userSlug}`);
          redirect(`/${rolePrefix}/${userSlug}`);
        }
      } else {
        console.log('No user data in result:', result);
        setError('Sign-in successful but no user data received. Please try again.');
      }
    } catch (error) {
      if (error instanceof Error && error.message === 'WAITLIST_NOT_APPROVED') {
        setError('Your account is awaiting approval. Please check back later.');
      } else if (error instanceof Error && error.message === 'WAITLIST_REQUIRED') {
        setError('Your email is not on the waitlist. Please sign up first.');
      } else {
        setError(error instanceof Error ? error.message : 'Invalid OTP code');
      }
      throw error; // Re-throw to let FamilyStyleOTP handle the error state
    } finally {
      setLoading(false);
    }
  };

  // Resend OTP function
  const handleResendOtp = async () => {
    if (selectedRole === 'teacher') {
      // Resend OTP for teacher
      setLoading(true);

      // Clear any pending message timeouts
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }

      setMessage('');
      setMessageType('');
      try {
        try {
          const result = await emailOtp.sendVerificationOtp({
            email: email.trim(),
            type: 'sign-in'
          });
          if (result.error) {
            const errorData = result.error as any;
            
            // Better Auth wraps server errors, so we need to check multiple levels
            let actualError = errorData;
            
            // If the error has a 'body' property (from fetch response), parse it
            if (errorData.body && typeof errorData.body === 'string') {
              try {
                actualError = JSON.parse(errorData.body);
              } catch (e) {
                actualError = { message: errorData.body };
              }
            }
            
            // Check if we have a nested error object
            if (actualError.error && typeof actualError.error === 'object') {
              actualError = actualError.error;
            }
            
            // Now handle the error based on code or message
            if (actualError.code) {
              switch (actualError.code) {
                case 'WAITLIST_REQUIRED':
                  throw new Error('Your email is not on the waitlist. Please sign up first.');
                case 'WAITLIST_NOT_APPROVED':
                  throw new Error('Your account is awaiting approval. Please check back later.');
                case 'EMAIL_SERVICE_ERROR':
                  throw new Error('Email service is temporarily unavailable. Please try again later.');
                case 'WAITLIST_VALIDATION_ERROR':
                  throw new Error('Unable to verify waitlist status. Please try again later.');
                default:
                  throw new Error(actualError.message || 'Failed to resend OTP');
              }
            } else {
              // Handle string error messages or generic objects
              const errorMessage = actualError.message || errorData.message || (typeof errorData === 'string' ? errorData : 'Failed to resend OTP');
              throw new Error(errorMessage);
            }
          }
          setMessage('OTP resent to your email.');
          setMessageType('success');

          // Clear success message after 3 seconds
          if (messageTimeoutRef.current) {
            clearTimeout(messageTimeoutRef.current);
          }
          messageTimeoutRef.current = setTimeout(() => {
            setMessage('');
            setMessageType('');
          }, 3000);
        } catch (otpError) {
          if (otpError instanceof Error) {
            if (otpError.message.includes('500') || otpError.message.includes('Internal Server Error')) {
              throw new Error('Email service is temporarily unavailable. Please try again later.');
            } else {
              throw new Error(otpError.message || 'Failed to resend OTP. Please try again.');
            }
          } else {
            throw new Error('Failed to resend OTP. Please try again.');
          }
        }
      } catch (error) {
        if (error instanceof Error) {
          if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
            setMessage('Server error occurred. Please try again later.');
            setMessageType('error');
          } else if (error.message.includes('Network Error') || error.message.includes('fetch')) {
            setMessage('Network error. Please check your connection and try again.');
            setMessageType('error');
          } else {
            setMessage(error.message);
            setMessageType('error');
          }
        } else {
          setMessage('Failed to resend OTP. Please try again.');
          setMessageType('error');
        }

        // Clear error message after 4 seconds
        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 4000);
      } finally {
        setLoading(false);
      }
    } else if (selectedRole === 'school_admin') {
      // Resend OTP for school admin
      setLoading(true);

      // Clear any pending message timeouts
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }

      setMessage('');
      setMessageType('');
      try {
        try {
          const result = await emailOtp.sendVerificationOtp({
            email: email.trim(),
            type: 'sign-in'
          });
          if (result.error) {
            const errorData = result.error as any;
            
            // Better Auth wraps server errors, so we need to check multiple levels
            let actualError = errorData;
            
            // If the error has a 'body' property (from fetch response), parse it
            if (errorData.body && typeof errorData.body === 'string') {
              try {
                actualError = JSON.parse(errorData.body);
              } catch (e) {
                actualError = { message: errorData.body };
              }
            }
            
            // Check if we have a nested error object
            if (actualError.error && typeof actualError.error === 'object') {
              actualError = actualError.error;
            }
            
            // Now handle the error based on code or message
            if (actualError.code) {
              switch (actualError.code) {
                case 'WAITLIST_REQUIRED':
                  throw new Error('Your email is not on the waitlist. Please sign up first.');
                case 'WAITLIST_NOT_APPROVED':
                  throw new Error('Your account is awaiting approval. Please check back later.');
                case 'EMAIL_SERVICE_ERROR':
                  throw new Error('Email service is temporarily unavailable. Please try again later.');
                case 'WAITLIST_VALIDATION_ERROR':
                  throw new Error('Unable to verify waitlist status. Please try again later.');
                default:
                  throw new Error(actualError.message || 'Failed to resend OTP');
              }
            } else {
              // Handle string error messages or generic objects
              const errorMessage = actualError.message || errorData.message || (typeof errorData === 'string' ? errorData : 'Failed to resend OTP');
              throw new Error(errorMessage);
            }
          }
          setMessage('OTP resent to your email.');
          setMessageType('success');

          // Clear success message after 3 seconds
          if (messageTimeoutRef.current) {
            clearTimeout(messageTimeoutRef.current);
          }
          messageTimeoutRef.current = setTimeout(() => {
            setMessage('');
            setMessageType('');
          }, 3000);
        } catch (otpError) {
          if (otpError instanceof Error) {
            if (otpError.message.includes('500') || otpError.message.includes('Internal Server Error')) {
              throw new Error('Email service is temporarily unavailable. Please try again later.');
            } else {
              throw new Error(otpError.message || 'Failed to resend OTP. Please try again.');
            }
          } else {
            throw new Error('Failed to resend OTP. Please try again.');
          }
        }
      } catch (error) {
        if (error instanceof Error) {
          if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
            setMessage('Server error occurred. Please try again later.');
            setMessageType('error');
          } else if (error.message.includes('Network Error') || error.message.includes('fetch')) {
            setMessage('Network error. Please check your connection and try again.');
            setMessageType('error');
          } else {
            setMessage(error.message);
            setMessageType('error');
          }
        } else {
          setMessage('Failed to resend OTP. Please try again.');
          setMessageType('error');
        }

        // Clear error message after 4 seconds
        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 4000);
      } finally {
        setLoading(false);
      }
    }
  };

  // Mouse interaction handlers for visual effects
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!inputRef.current) return;
    const rect = inputRef.current.getBoundingClientRect();
    setPosition({ x: e.clientX - rect.left, y: e.clientY - rect.top });
  };

  const handleMouseEnter = () => setOpacity(1);
  const handleMouseLeave = () => setOpacity(0);

  // Create wheel picker options for role selection
  const roleOptions: WheelPickerOption[] = [
    { value: 'student', label: 'Student' },
    { value: 'teacher', label: 'Teacher' },
    { value: 'school_admin', label: 'School' },
  ];

  // Role selection step
  if (currentStep === 'role-selection') {
    return (
      <div className="w-full relative">
        
        {/* Fixed NoiseText Header */}
        <div className="flex-shrink-0 fixed top-[13.5vh] left-0 right-0 z-10">
          <div className="text-center">
          <div className="flex flex-col justify-center items-center text-center w-full mx-auto relative">
              <div className="mb-1">
              <Badge
                variant="outline"
                className="font-manrope_1 text-xs w-fit"
              >
                Choose role
              </Badge>
            </div>
            <NoiseText
              text="Sign in"
              className="font-manrope_1 text-4xl md:text-5xl lg:text-6xl font-bold leading-tight"
            />
          
          </div>
            <p className="font-manrope_1 text-sm md:text-lg text-black/70 dark:text-white/70 mt-4 leading-relaxed">
              Select how you want to sign in
            </p>
          </div>
        </div>

        {/* Content below fixed header */}
        <div className="fixed top-[40vh] w-full left-0 right-0 md:max-w-[30vw] mx-auto px-4">
          <div className="relative w-full max-w-[400px] mx-auto space-y-8">
            <CustomWheelPicker
              options={roleOptions}
              value={provisionalRole}
              onValueChange={(value) => setProvisionalRole(value as UserRole)}
              className="w-full"
            />

            {/* Continue Button using InputButton style */}
            <div className="relative w-full max-w-[400px] mx-auto">
              <InputButtonProvider
                showInput={false}
                setShowInput={() => {}}
                transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                className="relative group w-full items-center justify-center h-12 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]"
                onMouseMove={handleMouseMove}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <InputButton>
                  <button
                    onClick={() => handleRoleSelection(provisionalRole)}
                    className="w-full h-full flex items-center justify-center text-inherit font-medium font-manrope_1"
                  >
                    Continue as {provisionalRole === 'school_admin' ? 'School' : provisionalRole.charAt(0).toUpperCase() + provisionalRole.slice(1)}
                  </button>

                  <div
                    ref={inputRef}
                    className="pointer-events-none absolute inset-0 rounded-full border-2 border-orange-500/50 dark:border-orange-400/50 transition-opacity duration-500"
                    style={{
                      opacity,
                      WebkitMaskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                      maskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                    } as React.CSSProperties}
                  />
                </InputButton>
              </InputButtonProvider>
            </div>

            {/* Keyboard navigation hint */}
            <div className="text-center">
              <p className="text-xs hidden md:block text-black/70 dark:text-white/70 font-manrope_1">
                Use Tab, Shift+Tab, or ↑↓ arrow keys to navigate
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // OTP verification step
  if (otpStep) {
    return (
      <div className="w-full flex flex-col">
        {/* Header */}
        <div className="flex-shrink-0 fixed top-[13.5vh] left-0 right-0 z-10">
         <div className="text-center">
          <div className="flex flex-col justify-center items-center text-center w-full mx-auto relative">
              <div className="mb-1">
              <Badge
                variant="outline"
                className="font-manrope_1 text-xs w-fit"
              >
                Enter code
              </Badge>
            </div>
            <NoiseText    
              text="Verification Code"          
              className="font-manrope_1 text-4xl md:text-5xl lg:text-6xl font-bold leading-tight"
            />
            <p className="font-manrope_1 text-md md:text-xl text-black/70 dark:text-white/70 mt-2 leading-relaxed">
              <button
                onClick={handleBack}
              className="font-manrope_1 text-sm text-black/60 dark:text-white/60 underline"
              >
                ← Back
              </button>
            </p>
          </div>
        </div>
        </div>

        {/* Content */}
        <div className="fixed top-[40vh] w-full left-0 right-0 md:max-w-[30vw] mx-auto px-4">
          <div className="relative w-full max-w-[400px] mx-auto">
            {/* OTP Input Container with consistent styling */}
            <div className="relative group w-full mt-2 items-center justify-center select-none  text-sm transition-all duration-200 overflow-hidden ">
              <FamilyStyleOTP
                value={otp}
                onChange={setOtp}
                onSubmit={handleOtpVerification}
                loading={loading}
                error={error}
                email={showOtpDirectly ? undefined : email} // Don't show email if accessed directly
                onResend={showOtpDirectly ? undefined : handleResendOtp} // Don't show resend if accessed directly
                directAccess={showOtpDirectly} // Pass flag to indicate direct access
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Credentials step
  return (
    <div className="w-full  relative">
      {/* Fixed NoiseText Header */}
      <div className="fixed top-[13.5vh] left-0 right-0 z-10">
        <div className="text-center relative w-full">
          <div className="flex flex-col justify-center items-center text-center w-full mx-auto relative">
         <div className="mb-1">
            <Badge
              variant="outline"
              className="font-manrope_1 text-xs w-fit"
            >
              Sign in
            </Badge>
            </div>
          <NoiseText
            text={`${selectedRole === 'school_admin' ? 'School' : selectedRole.charAt(0).toUpperCase() + selectedRole.slice(1)}`}
            className="font-manrope_1 text-center text-4xl md:text-5xl lg:text-6xl font-bold leading-tight"
          />
            
          </div>
          <p className="font-manrope_1 text-md md:text-xl text-black/70 dark:text-white/70 mt-2 leading-relaxed">
            <button
              onClick={handleBack}
              className="font-manrope_1 text-sm text-black/60 dark:text-white/60 underline"
            >
              ← Change role
            </button>
          </p>
        </div>
      </div>

      {/* Content below fixed header */}
        <div className="fixed top-[40vh] w-full left-0 right-0 md:max-w-[30vw] mx-auto px-4">
        {/* Student Login Form */}
        {selectedRole === 'student' && (
          <div className="relative w-full max-w-[400px] mx-auto">
            <form onSubmit={handleStudentLogin} className="relative gap-4 justify-center flex flex-col">
                <p className="font-manrope_1 text-center text-sm md:text-lg text-black/70 dark:text-white/70 mt-4 leading-relaxed">
              Sign in as a student
            </p>
              <InputButtonProvider
                showInput={showInput}
                setShowInput={setShowInput}
                transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                className="relative group w-full items-center justify-center h-12 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]"
                onMouseMove={handleMouseMove}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <InputButton>
                  <AnimatePresence>
                    {!showInput && (
                      <motion.div
                        key="action-text"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="w-full h-full"
                      >
                        <InputButtonAction>
                          <div className="flex items-center gap-2 font-manrope_1">
                            {studentStep === 'username' ? 'Enter your username or email' : 'Enter your password'}
                          </div>
                        </InputButtonAction>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  <InputButtonSubmit
                    onClick={() => {}}
                    type="submit"
                    disabled={loading || (studentStep === 'username' && !username.trim() && showInput) || (studentStep === 'password' && !password.trim() && showInput)}
                    message={message}
                    messageType={messageType}
                    isSubmitting={loading}
                    success={false}
                    className="disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? (
                      <motion.span
                        key="pending"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Loader2 className="animate-spin" />
                      </motion.span>
                    ) : (
                      studentStep === 'username' ? 'Continue' : 'Sign in'
                    )}
                  </InputButtonSubmit>

                  {showInput && (
                    <div className="flex items-center w-full pl-0">
                      {studentStep === 'username' ? (
                        <InputButtonInput
                          type="text"
                          placeholder="Enter your username or email"
                          value={username}
                          onChange={(e) => setUsername(e.target.value)}
                          disabled={loading}
                          required
                          autoFocus
                          autoComplete="username"
                        />
                      ) : (
                        <InputButtonInput
                          type="password"
                          placeholder="Enter your password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          disabled={loading}
                          required
                          autoFocus
                          autoComplete="current-password"
                        />
                      )}
                    </div>
                  )}

                  <div
                    ref={inputRef}
                    className="pointer-events-none absolute inset-0 rounded-full border-2 border-orange-500/50 dark:border-orange-400/50 transition-opacity duration-500"
                    style={{
                      opacity,
                      WebkitMaskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                      maskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                    } as React.CSSProperties}
                  />
                </InputButton>
              </InputButtonProvider>
            </form>
            {/* Reserved space for helper text to prevent layout shift */}
            <div className="mt-2 h-5 flex items-center justify-center">
              {showInput && (
                <p className="font-manrope_1 text-xs text-black/70 dark:text-white/70 text-center">
                  Your username was created by your teacher
                </p>
              )}
            </div>
          </div>
        )}

        {/* Teacher Login Form */}
        {selectedRole === 'teacher' && (
          <div className="relative w-full max-w-[400px] mx-auto">
            <form onSubmit={handleTeacherLogin} className="relative gap-4 justify-center flex flex-col ">
               <p className="font-manrope_1 text-center text-sm md:text-lg text-black/70 dark:text-white/70 mt-4 leading-relaxed">
              Sign in as a teacher
            </p>
              <InputButtonProvider
                showInput={showInput}
                setShowInput={setShowInput}
                transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                className="relative group w-full items-center justify-center h-12 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]"
                onMouseMove={handleMouseMove}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <InputButton>
                  <AnimatePresence>
                    {!showInput && (
                      <motion.div
                        key="action-text"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="w-full h-full"
                      >
                        <InputButtonAction>
                          <div className="flex items-center gap-2 font-manrope_1">
                            Enter your email address
                          </div>
                        </InputButtonAction>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  <InputButtonSubmit
                    onClick={() => {}}
                    type="submit"
                    disabled={loading || (!email.includes('@') && showInput)}
                    message={message}
                    messageType={messageType}
                    isSubmitting={loading}
                    success={messageType === 'success'}
                    className="disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? (
                      <motion.span
                        key="pending"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Loader2 className="animate-spin" />
                      </motion.span>
                    ) : (
                      'Send Verification Code'
                    )}
                  </InputButtonSubmit>

                  {showInput && (
                    <div className="flex items-center w-full pl-0">
                      <InputButtonInput
                        type="email"
                        placeholder="Enter your email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        disabled={loading}
                        required
                        autoFocus
                        autoComplete="email"
                      />
                    </div>
                  )}

                  <div
                    ref={inputRef}
                    className="pointer-events-none absolute inset-0 rounded-full border-2 border-orange-500/50 dark:border-orange-400/50 transition-opacity duration-500"
                    style={{
                      opacity,
                      WebkitMaskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                      maskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                    } as React.CSSProperties}
                  />
                </InputButton>
              </InputButtonProvider>
            </form>

            {/* "I have a valid OTP code" option - only show when input is not shown */}
            {!showInput && (
              <div className="mt-2 flex items-center justify-center">
                <button
                  onClick={handleTeacherOtpDirect}
                  className="font-manrope_1 text-xs text-black/60 dark:text-white/60 underline hover:text-black/80 dark:hover:text-white/80 transition-colors"
                >
                  I have a valid OTP code
                </button>
              </div>
            )}

            {/* Reserved space for helper text to prevent layout shift */}
            <div className="mt-2 h-5 flex items-center justify-center">
              {showInput && (
                <p className="font-manrope_1 text-xs text-black/70 dark:text-white/70 text-center">
                  We'll send a verification code to your email
                </p>
              )}
            </div>
          </div>
        )}

        {/* School Admin Login Form */}
        {selectedRole === 'school_admin' && (
          <div className="relative w-full max-w-[400px] mx-auto">
            <form onSubmit={handleSchoolLogin} className="relative gap-4 justify-center flex flex-col">
               <p className="font-manrope_1 text-center text-sm md:text-lg text-black/70 dark:text-white/70 mt-4 leading-relaxed">
              Sign in as school admin
            </p>
              <InputButtonProvider
                showInput={showInput}
                setShowInput={setShowInput}
                transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                className="relative group w-full items-center justify-center h-12 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]"
                onMouseMove={handleMouseMove}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <InputButton>
                  <AnimatePresence>
                    {!showInput && (
                      <motion.div
                        key="action-text"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="w-full h-full"
                      >
                        <InputButtonAction>
                          <div className="flex items-center gap-2 font-manrope_1">
                            Enter your email address
                          </div>
                        </InputButtonAction>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  <InputButtonSubmit
                    onClick={() => {}}
                    type="submit"
                    disabled={loading || (!email.includes('@') && showInput)}
                    message={message}
                    messageType={messageType}
                    isSubmitting={loading}
                    success={messageType === 'success'}
                    className="disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? (
                      <motion.span
                        key="pending"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Loader2 className="animate-spin" />
                      </motion.span>
                    ) : (
                      'Send Verification Code'
                    )}
                  </InputButtonSubmit>

                  {showInput && (
                    <div className="flex items-center w-full pl-0">
                      <InputButtonInput
                        type="email"
                        placeholder="Enter your email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        disabled={loading}
                        required
                        autoFocus
                        autoComplete="email"
                      />
                    </div>
                  )}

                  <div
                    ref={inputRef}
                    className="pointer-events-none absolute inset-0 rounded-full border-2 border-orange-500/50 dark:border-orange-400/50 transition-opacity duration-500"
                    style={{
                      opacity,
                      WebkitMaskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                      maskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                    } as React.CSSProperties}
                  />
                </InputButton>
              </InputButtonProvider>
            </form>

            {/* "I have a valid OTP code" option - only show when input is not shown */}
            {!showInput && (
              <div className="mt-3 flex items-center justify-center">
                <button
                  onClick={handleSchoolOtpDirect}
                  className="font-manrope_1 text-xs text-black/60 dark:text-white/60 underline hover:text-black/80 dark:hover:text-white/80 transition-colors"
                >
                  I have a valid OTP code
                </button>
              </div>
            )}

            {/* Reserved space for helper text to prevent layout shift */}
            <div className="mt-2 h-5 flex items-center justify-center">
              {showInput && (
                <p className="font-manrope_1 text-xs text-black/70 dark:text-white/70 text-center">
                  We'll send a verification code to your email
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoginForm;