// components/waitlist-signup.tsx
import { useState, useRef, useEffect, useCallback } from 'react';
import useClickOutside from '../../hooks/useClickOutside';

import {
  InputButton,
  InputButtonProvider,
  InputButtonAction,
  InputButtonSubmit,
  InputButtonInput,
} from '../ui/buttons/input';
import { Check, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'motion/react';
import { StudentIcon, TeacherIcon, SchoolIcon, EmailIcon, SearchIcon } from '../ui/icons';

interface School {
  place_id: string;
  name: string;
  formatted_address: string;
  location?: { lat: number; lng: number };
  viewport?: any;
}

interface WaitlistSignupProps {
  userType: 'student' | 'teacher' | 'school_admin';
  placeholder?: string;
  submitText?: string;
  apiKey: string;
}

// Declare Google Maps types
declare global {
  interface Window {
    google: any;
    initGooglePlaces: () => void;
  }
}

// Define specific types for Google Maps Web Components
interface MapElement extends HTMLElement {
  innerMap: google.maps.Map;
  center: google.maps.LatLng | google.maps.LatLngLiteral;
  zoom: number;
  'map-id'?: string;
}

interface AdvancedMarkerElement extends HTMLElement {
  position: google.maps.LatLng | google.maps.LatLngLiteral | null;
}

// Extend JSX to include Google's web components
declare global {
  namespace JSX {
    interface IntrinsicElements {
      'gmp-map': React.HTMLAttributes<MapElement> & { 
        ref?: React.Ref<MapElement>;
        center?: google.maps.LatLng | google.maps.LatLngLiteral;
        zoom?: number;
        'map-id'?: string;
      };
      'gmp-advanced-marker': React.HTMLAttributes<AdvancedMarkerElement> & { 
        ref?: React.Ref<AdvancedMarkerElement>; 
        position?: google.maps.LatLng | google.maps.LatLngLiteral | null;
      };
    }
  }
}

export const WaitlistSignup: React.FC<WaitlistSignupProps> = ({
  userType,
  placeholder = "Enter your email address",
  submitText = "Join Waitlist",
  apiKey
}) => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [school, setSchool] = useState<School | null>(null);
  const [schoolQuery, setSchoolQuery] = useState('');
  const [schoolSuggestions, setSchoolSuggestions] = useState<School[]>([]);
  const [showSchoolSearch, setShowSchoolSearch] = useState(false);
  const [isLoadingSchools, setIsLoadingSchools] = useState(false);
  const [registeredSchoolIds, setRegisteredSchoolIds] = useState<string[]>([]);
  const [currentStep, setCurrentStep] = useState<'school' | 'name' | 'email'>(
    userType === 'school_admin' ? 'school' : 'name'
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);
  const [showInput, setShowInput] = useState(false);
  const [success, setSuccess] = useState(false);
  const [showStudentError, setShowStudentError] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [googleMapsLoaded, setGoogleMapsLoaded] = useState(false);
  const [mapCenter, setMapCenter] = useState<google.maps.LatLngLiteral>({ lat: 56.2639, lng: 9.5018 });
  const [mapZoom, setMapZoom] = useState<number>(7);
  const [markerPosition, setMarkerPosition] = useState<google.maps.LatLngLiteral | null>(null);

  const inputRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLDivElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const messageTimeoutRef = useRef<NodeJS.Timeout>();
  const mapRef = useRef<MapElement>(null);
  const markerRef = useRef<AdvancedMarkerElement>(null);
  const [predictions, setPredictions] = useState<google.maps.places.AutocompletePrediction[]>([]);
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);

  // Load Google Maps API - ensure correct libraries are loaded for map components
  useEffect(() => {
    const loadGoogleMaps = async () => {
      if (window.google?.maps?.places) {
        setGoogleMapsLoaded(true);
        return;
      }

      if (document.querySelector('script[src*="maps.googleapis.com"]')) {
        // If a script is already there but google object not ready, wait for it.
        window.initGooglePlaces = () => setGoogleMapsLoaded(true);
        if (window.google) setGoogleMapsLoaded(true);
        return;
      }

      try {
        const script = document.createElement('script');
        // The new web components load their own dependencies.
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places,marker&v=beta&callback=initGooglePlaces`;
        script.async = true;

        window.initGooglePlaces = () => {
          setGoogleMapsLoaded(true);
        };

        script.onerror = () => {
          console.error('Failed to load Google Maps API');
          setGoogleMapsLoaded(false); // Fallback to backend search can be triggered
        };

        document.head.appendChild(script);
      } catch (error) {
        console.error('Error loading Google Maps:', error);
      }
    };

    if (userType === 'school_admin') {
      loadGoogleMaps();
    }
  }, [apiKey, userType]);

  // Initialize Google services once the API is loaded
  useEffect(() => {
    if (googleMapsLoaded && mapRef.current?.innerMap) {
      if (!autocompleteService.current) {
        autocompleteService.current = new google.maps.places.AutocompleteService();
      }
      if (!placesService.current) {
        placesService.current = new google.maps.places.PlacesService(mapRef.current.innerMap);
      }
    }
  }, [googleMapsLoaded, showSchoolSearch]);

  // Fetch registered schools on component mount
  useEffect(() => {
    const fetchRegisteredSchools = async () => {
      try {
        const response = await fetch('/api/registered-schools');
        if (response.ok) {
          const data = await response.json();
          setRegisteredSchoolIds(data.registeredPlaceIds || []);
        }
      } catch (error) {
        console.error('Failed to fetch registered schools:', error);
      }
    };

    fetchRegisteredSchools();
  }, []);

  // Fetch autocomplete predictions when the search query changes
  useEffect(() => {
    if (!autocompleteService.current || !schoolQuery || !showSchoolSearch) {
      setPredictions([]);
      return;
    }

    const handler = setTimeout(() => {
      autocompleteService.current?.getPlacePredictions(
        { input: schoolQuery, types: ['school'] },
        (results) => {
          setPredictions(results || []);
        }
      );
    }, 300);

    return () => clearTimeout(handler);
  }, [schoolQuery, showSchoolSearch]);

  const handlePredictionSelect = (prediction: google.maps.places.AutocompletePrediction) => {
    if (!placesService.current || !mapRef.current || !markerRef.current) return;

    // Check if this school is already registered
    if (registeredSchoolIds.includes(prediction.place_id)) {
      return; // Don't allow selection of already registered schools
    }

    placesService.current.getDetails(
      { placeId: prediction.place_id, fields: ['name', 'formatted_address', 'geometry', 'place_id'] },
      (place, status) => {
        if (status === google.maps.places.PlacesServiceStatus.OK && place?.geometry?.location) {
          const location = place.geometry.location;
          const newPosition = { lat: location.lat(), lng: location.lng() };

          const selectedSchool: School = {
            place_id: place.place_id!,
            name: place.name!,
            formatted_address: place.formatted_address!,
            location: newPosition,
            viewport: place.geometry.viewport || null,
          };

          setSchool(selectedSchool);
          setSchoolQuery(selectedSchool.name); // Update input to show full name
          setPredictions([]); // Hide predictions

          // Update map state to re-center and place marker
          setMapCenter(newPosition);
          setMapZoom(17);
          setMarkerPosition(newPosition);
        }
      }
    );
  };

  // Reset form when userType changes
  useEffect(() => {
    // Immediately set the correct step for the new userType to prevent flashing
    setCurrentStep(userType === 'school_admin' ? 'school' : 'name');
    // Also immediately reset input visibility to prevent flashing
    setShowInput(false);
    setShowSchoolSearch(false);
    resetForm();
  }, [userType]);

  const resetForm = useCallback(() => {
    // Clear any pending message timeouts
    if (messageTimeoutRef.current) {
      clearTimeout(messageTimeoutRef.current);
    }

    setName('');
    setEmail('');
    setSchool(null);
    setSchoolQuery('');
    setSchoolSuggestions([]);
    setShowSchoolSearch(false);
    // Don't set currentStep here since it's already set in useEffect
    setShowInput(false);
    setMessage('');
    setMessageType('');
    setSuccess(false);
    setIsSubmitting(false);
    setShowStudentError(false);
    setIsResetting(false);
  }, []);

  const resetFormWithDelay = useCallback(() => {
    setIsResetting(true);
    setShowInput(false);
    setShowStudentError(false);
    setShowSchoolSearch(false);
    // Ensure correct step is set immediately
    setCurrentStep(userType === 'school_admin' ? 'school' : 'name');

    setTimeout(() => {
      resetForm();
    }, 200);
  }, [resetForm, userType]);

  // Handle click outside
  useClickOutside(formRef, () => {
    if ((showInput || showStudentError || showSchoolSearch) && !isSubmitting) {
      resetFormWithDelay();
    }
  });

  // Fallback: Backend school search (only if Google Maps fails to load)
  const searchSchoolsBackend = useCallback(async (query: string) => {
    if (query.length < 3) {
      setSchoolSuggestions([]);
      return;
    }

    setIsLoadingSchools(true);
    try {
      const response = await fetch('/api/search-schools', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      if (!response.ok) {
        console.error('Backend search failed:', response.status);
        setSchoolSuggestions([]);
        return;
      }

      const data = await response.json();
      if (data.schools) {
        setSchoolSuggestions(data.schools);
      } else {
        setSchoolSuggestions([]);
      }

    } catch (error) {
      console.error('Backend search error:', error);
      setSchoolSuggestions([]);
    } finally {
      setIsLoadingSchools(false);
    }
  }, []);

  // Debounced backend search (fallback)
  useEffect(() => {
    if (!googleMapsLoaded && showSchoolSearch) {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      searchTimeoutRef.current = setTimeout(() => {
        if (schoolQuery) {
          searchSchoolsBackend(schoolQuery);
        }
      }, 300);

      return () => {
        if (searchTimeoutRef.current) {
          clearTimeout(searchTimeoutRef.current);
        }
      };
    }
  }, [schoolQuery, showSchoolSearch, googleMapsLoaded, searchSchoolsBackend]);

  const handleSchoolSelect = (selectedSchool: School) => {
    // Check if this school is already registered
    if (registeredSchoolIds.includes(selectedSchool.place_id)) {
      return; // Don't allow selection of already registered schools
    }

    setSchool(selectedSchool);
    setSchoolQuery(selectedSchool.name);
    setSchoolSuggestions([]);
    setShowSchoolSearch(false);
    setCurrentStep('name');
    setShowInput(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // For students, the feature is not yet available. Just show the error state and stop.
    if (userType === 'student') {
      setShowStudentError(true);
      return;
    }

    if (userType === 'school_admin') {
      if (currentStep === 'school') {
        // If search UI isn't visible yet, show it.
        if (!showSchoolSearch) {
          setShowSchoolSearch(true);
          return;
        }
        // If a school has been selected, move to the name step.
        if (school) {
          setCurrentStep('name');
          setShowInput(true);
          return;
        }
        // Otherwise, do nothing (button should be disabled).
        return;
      } else if (currentStep === 'name') {
        // Validate name and move to email step
        if (!name.trim()) {
          setMessage('Please enter your full name');
          setMessageType('error');

          if (messageTimeoutRef.current) {
            clearTimeout(messageTimeoutRef.current);
          }
          messageTimeoutRef.current = setTimeout(() => {
            setMessage('');
            setMessageType('');
          }, 3000);
          return;
        }
        setCurrentStep('email');
        return;
      }
    } else {
      // For teachers, handle name step first
      if (currentStep === 'name') {
        if (!showInput) {
          setShowInput(true);
          return;
        }
        // Validate name and move to email step
        if (!name.trim()) {
          setMessage('Please enter your full name');
          setMessageType('error');

          if (messageTimeoutRef.current) {
            clearTimeout(messageTimeoutRef.current);
          }
          messageTimeoutRef.current = setTimeout(() => {
            setMessage('');
            setMessageType('');
          }, 3000);
          return;
        }
        setCurrentStep('email');
        return;
      } else if (currentStep === 'email') {
        // For email step, just show the input on the first click.
        if (!showInput) {
          setShowInput(true);
          return;
        }
      }
    }

    // --- The rest of the function now only deals with the final email submission --- 

    if (!email || !email.includes('@')) {
      setMessage('Please enter a valid email address');
      setMessageType('error');
      
      // Clear validation error message after 3 seconds
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
      messageTimeoutRef.current = setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 3000);
      return;
    }

    setIsSubmitting(true);
    setMessage('');
    setMessageType('');

    try {
      const response = await fetch('/api/join-waitlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          userType,
          name: name.trim(),
          school: userType === 'school_admin' ? school : undefined,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setSuccess(true);
        setMessage('You have joined the waitlist! Check your email.');
        setMessageType('success');
        
        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          resetForm();
        }, 5000);
      } else {
        if (result.error === 'Email already exists') {
          setMessage('This email is already on our waitlist!');
          setMessageType('error');
        } else if (result.error === 'School already registered') {
          setMessage('This school is already registered on our waitlist!');
          setMessageType('error');
        } else {
          setMessage(result.error || 'Something went wrong. Please try again.');
          setMessageType('error');
        }
        
        // Clear error message after 4 seconds
        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 4000);
      }
    } catch (error) {
      console.error('Waitlist signup error:', error);
      setMessage('Please check your connection and try again.');
      setMessageType('error');
      
      // Clear error message after 4 seconds
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
      messageTimeoutRef.current = setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 4000);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getDisplayText = () => {
    // If the student error is showing, this message takes priority.
    if (showStudentError) {
      return "Student Sign In - Coming Soon!";
    }

    switch (userType) {
      case 'student':
        return "I'm a Student";
      case 'teacher':
        if (currentStep === 'name') {
          return 'Enter your full name';
        }
        return 'Join our waitlist as a Teacher';
      case 'school_admin':
        if (currentStep === 'school') {
          return 'Search for your School';
        } else if (currentStep === 'name') {
          return 'Enter your full name';
        }
        return 'Join our waitlist as a School';
    }
  };

  const getSubmitText = () => {
    if (userType === 'student') {
      return 'Sign In';
    }
    if (userType === 'school_admin') {
      if (currentStep === 'school') {
        return 'Select School';
      } else if (currentStep === 'name') {
        return 'Continue';
      }
    }
    if (userType === 'teacher' && currentStep === 'name') {
      return 'Continue';
    }
    return submitText;
  };

  const isStudentMode = userType === 'student';
  const isSchoolSearchStep = userType === 'school_admin' && currentStep === 'school';
  const isNameStep = currentStep === 'name';
  const isEmailStep = currentStep === 'email';
  const shouldShowNameInput = isNameStep && showInput;
  const shouldShowEmailInput = isEmailStep && showInput;
  const shouldShowSchoolSearch = isSchoolSearchStep && showSchoolSearch;

  const isSubmitDisabled = (() => {
    if (isStudentMode) return false; // Student mode is always enabled for error display

    if (userType === 'school_admin') {
      if (currentStep === 'school') return !school;
      if (currentStep === 'name') return !name.trim();
      if (currentStep === 'email') return !email.includes('@') || !school;
    } else if (userType === 'teacher') {
      if (currentStep === 'name') return !name.trim();
      if (currentStep === 'email') return !email.includes('@');
    }

    return false;
  })();

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!inputRef.current) return;
    const rect = inputRef.current.getBoundingClientRect();
    setPosition({ x: e.clientX - rect.left, y: e.clientY - rect.top });
  };

  const handleMouseEnter = () => setOpacity(1);
  const handleMouseLeave = () => setOpacity(0);

  return (
    <div className="relative w-full h-full max-w-[400px] mx-auto" ref={formRef}>
      <form onSubmit={handleSubmit} className="relative">
        <InputButtonProvider
          showInput={!isStudentMode && (shouldShowNameInput || shouldShowEmailInput || shouldShowSchoolSearch)}
          setShowInput={isSchoolSearchStep ? setShowSchoolSearch : setShowInput}
          transition={{ type: 'spring', stiffness: 400, damping: 30 }}
          className={`relative group w-full items-center justify-center h-12 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden ${
            showStudentError
              ? 'bg-gradient-to-br from-zinc-100 via-zinc-200 to-red-50/40 dark:from-[#212026] dark:via-[#212026] dark:to-red-950/40 text-red-700 dark:text-red-300 placeholder:text-red-700 dark:placeholder:text-red-300'
              : 'bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]'
          }`}
          onMouseMove={handleMouseMove}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          
          <InputButton>
            <AnimatePresence>
              {((!shouldShowNameInput && !shouldShowEmailInput && !shouldShowSchoolSearch) || showStudentError) && !isResetting && (
                <motion.div
                  key="action-text"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="w-full h-full"
                >
                  {userType === 'student' ? (
                    <button type="submit" className="w-full h-full flex items-center justify-start text-inherit font-medium">
                      <div className="flex items-center gap-2 font-manrope_1">
                        <StudentIcon className="w-4 h-4" />
                        {getDisplayText()}
                      </div>
                    </button>
                  ) : (
                    <InputButtonAction>
                      <div className="flex items-center gap-2">
                        {userType === 'teacher' && <TeacherIcon className="w-4 h-4" />}
                        {userType === 'school_admin' && !school && <SearchIcon className="w-4 h-4" />}
                        {getDisplayText()}
                      </div>
                    </InputButtonAction>
                  )}
                </motion.div>
              )}
            </AnimatePresence>

            <InputButtonSubmit
              onClick={() => {}}
              type="submit"
              disabled={isSubmitting || (isSubmitDisabled && !showStudentError)}
              message={message}
              messageType={messageType}
              isSubmitting={isSubmitting}
              success={success}
              className={`${showStudentError ? 'opacity-50 cursor-not-allowed' : ''} disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              {success ? (
                <motion.span
                  key="success"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <Check />
                </motion.span>
              ) : isSubmitting ? (
                <motion.span
                  key="pending"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <Loader2 className="animate-spin" />
                </motion.span>
              ) : (
                getSubmitText()
              )}
            </InputButtonSubmit>

            {(shouldShowNameInput || shouldShowEmailInput || shouldShowSchoolSearch) && !isStudentMode && (
              <div className="flex items-center w-full pl-0">
                <InputButtonInput
                  type={isSchoolSearchStep ? 'text' : (shouldShowNameInput ? 'text' : 'email')}
                  placeholder={
                    isSchoolSearchStep
                      ? 'Search school address or city'
                      : shouldShowNameInput
                        ? 'Enter your full name'
                        : placeholder
                  }
                  value={
                    isSchoolSearchStep
                      ? schoolQuery
                      : shouldShowNameInput
                        ? name
                        : email
                  }
                  onChange={
                    isSchoolSearchStep
                      ? (e) => setSchoolQuery(e.target.value)
                      : shouldShowNameInput
                        ? (e) => setName(e.target.value)
                        : (e) => setEmail(e.target.value)
                  }
                  disabled={isSubmitting}
                  required
                  autoFocus
                />
              </div>
            )}

            <div
              ref={inputRef}
              className="pointer-events-none absolute inset-0 rounded-full border-2 border-orange-500/50 transition-opacity duration-500"
              style={{
                opacity,
                WebkitMaskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                maskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
              }}
            />
          </InputButton>
        </InputButtonProvider>


      </form>

      {/* Container for Google Place Picker and Map */}
      <AnimatePresence>
        {userType === 'school_admin' && showSchoolSearch && (
          <motion.div
            initial={{ opacity: 0, y: -10, height: 0 }}
            animate={{ opacity: 1, y: 0, height: 'auto' }}
            exit={{ opacity: 0, y: -10, height: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed w-full max-w-[400px] left-1/2 transform -translate-x-1/2 h-[350px] z-[**********] mt-2"
          >
            <div className="relative p-2 rounded-lg bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]">

              <div className="relative">
                {googleMapsLoaded ? (
                  <gmp-map
                    ref={mapRef}
                    center={mapCenter}
                    zoom={mapZoom}
                    map-id="fb0295e7ee732af67595084e"
                    style={{ height: '350px', borderRadius: '12px' }}
                  >
                    <gmp-advanced-marker ref={markerRef} position={markerPosition}></gmp-advanced-marker>
                  </gmp-map>
                ) : (
                  <div className="flex items-center justify-center h-[350px]">
                    <Loader2 className="w-6 h-6 animate-spin text-zinc-500" />
                    <span className="ml-2 font-manrope_1 text-xs text-black/80 dark:text-white/80">Loading Map...</span>
                  </div>
                )}

                {/* Autocomplete predictions overlay - positioned directly over the map */}
                <AnimatePresence>
                  {predictions.length > 0 && (
                    <motion.div
                      className="absolute top-0 left-0 right-0 z-[9999]"
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                    >
                      <div className="backdrop-blur-md border border-zinc-200/60  rounded-b-lg shadow-2xl">
                        <ul className="max-h-60 overflow-y-auto">
                          {predictions.map((p) => {
                            const isRegistered = registeredSchoolIds.includes(p.place_id);
                            return (
                              <li
                                key={p.place_id}
                                onClick={() => !isRegistered && handlePredictionSelect(p)}
                                className={`px-4 py-3 text-left text-sm font-manrope_1 transition-colors border-b border-zinc-200/30 dark:border-zinc-700/30 last:border-b-0 ${
                                  isRegistered
                                    ? 'cursor-not-allowed opacity-60 bg-zinc-50/50  text-zinc-500 '
                                    : 'cursor-pointer hover:bg-zinc-100/70 text-black/90 '
                                }`}
                              >
                                <div className="flex items-center justify-between">
                                  <span className="flex-1">{p.description}</span>
                                  {isRegistered && (
                                    <span className="ml-2 px-2 py-1 text-xs font-manrope_1 rounded-full bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]">
                                      Already Registered
                                    </span>
                                  )}
                                </div>
                              </li>
                            );
                          })}
                        </ul>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Fallback school suggestions dropdown (only if Google Maps isn't loaded) */}
      <AnimatePresence>
        {!googleMapsLoaded && showSchoolSearch && schoolSuggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="fixed z-[99999999] w-full md:max-w-[400px] mt-2 bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg shadow-lg"
          >
            {schoolSuggestions.map((schoolOption) => {
              const isRegistered = registeredSchoolIds.includes(schoolOption.place_id);
              return (
                <button
                  key={schoolOption.place_id}
                  type="button"
                  onClick={() => !isRegistered && handleSchoolSelect(schoolOption)}
                  disabled={isRegistered}
                  className={`w-full px-4 py-3 text-left border-b border-zinc-100 dark:border-zinc-700 last:border-b-0 transition-colors ${
                    isRegistered
                      ? 'cursor-not-allowed opacity-60 bg-zinc-50/50 dark:bg-zinc-800/50'
                      : 'hover:bg-zinc-50 dark:hover:bg-zinc-700'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className={`font-medium ${isRegistered ? 'text-zinc-500 dark:text-zinc-400' : 'text-zinc-900 dark:text-zinc-100'}`}>
                        {schoolOption.name}
                      </div>
                      <div className="text-sm text-zinc-500 dark:text-zinc-400">
                        {schoolOption.formatted_address}
                      </div>
                    </div>
                    {isRegistered && (
                      <span className="ml-2 px-2 py-1 text-xs font-medium bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 rounded-full">
                        Already Registered
                      </span>
                    )}
                  </div>
                </button>
              );
            })}
            {isLoadingSchools && (
              <div className="px-4 py-3 text-center text-zinc-500 dark:text-zinc-400">
                <Loader2 className="w-4 h-4 animate-spin inline mr-2" />
                Searching schools...
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

    </div>
  );
};